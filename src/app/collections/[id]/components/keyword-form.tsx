'use client';

import { Button, Card, Input, Label, Translate } from '@/components/ui';
import { useKeywordsContext, useTranslation, useGuidance } from '@/contexts';
import { Plus, Languages, X } from 'lucide-react';
import { useCallback, useMemo, useState } from 'react';

export function KeywordForm() {
	const { t } = useTranslation();
	const { markUserInteraction } = useGuidance();
	const {
		keywords,
		createKeyword,
		deleteKeyword,
		selectedKeywords,
		setSelectedKeywords,
		isLoading: isFetchingKeywords,
	} = useKeywordsContext();
	const [newKeyword, setNewKeyword] = useState('');

	const handleAddKeyword = useCallback(async () => {
		if (!newKeyword.trim()) return;

		const existingKeyword = keywords.find(
			(kw) => kw.content.toLowerCase() === newKeyword.toLowerCase()
		);

		if (existingKeyword) {
			if (!selectedKeywords.includes(existingKeyword.id))
				setSelectedKeywords([...selectedKeywords, existingKeyword.id]);
		} else {
			const createdKeyword = await createKeyword(newKeyword);
			if (createdKeyword) setSelectedKeywords([...selectedKeywords, createdKeyword.id]);
		}
		setNewKeyword('');
	}, [newKeyword, keywords, selectedKeywords, createKeyword, setSelectedKeywords]);

	const handleKeyPress = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement>) => {
			if (e.key === 'Enter') {
				e.preventDefault();
				handleAddKeyword();
			}
		},
		[handleAddKeyword]
	);

	const handleRemoveKeyword = useCallback(
		(keywordId: string) => {
			setSelectedKeywords(selectedKeywords.filter((id) => id !== keywordId));
		},
		[selectedKeywords, setSelectedKeywords]
	);

	const handleDeleteKeyword = useCallback(
		async (keywordId: string) => {
			// Optimistic update: remove from state first
			const originalKeywords = keywords;
			const originalSelectedKeywords = selectedKeywords;

			// Remove from both keywords list and selected keywords
			const updatedKeywords = keywords.filter((k) => k.id !== keywordId);
			const updatedSelectedKeywords = selectedKeywords.filter((id) => id !== keywordId);

			// Update state immediately for better UX
			setSelectedKeywords(updatedSelectedKeywords);

			try {
				// Call the delete function from context
				await deleteKeyword(keywordId);
			} catch (error) {
				// If delete fails, restore the original state
				setSelectedKeywords(originalSelectedKeywords);
				console.error('Failed to delete keyword:', error);
			}
		},
		[keywords, selectedKeywords, setSelectedKeywords, deleteKeyword]
	);

	const filteredKeywords = useMemo(() => keywords, [keywords]);

	return (
		<div className="space-y-4">
			<Card className="p-7 rounded-2xl shadow-2xl bg-card">
				<div className="flex flex-col gap-3">
					<div className="flex items-center gap-2 mb-2">
						<Languages className="h-6 w-6 text-primary" />
						<Label className="text-lg font-bold text-primary tracking-tight">
							<Translate text="words.select_keywords" />
						</Label>
					</div>

					<div className="flex gap-3 items-center">
						<div className="relative flex-1">
							<Input
								type="text"
								value={newKeyword}
								onChange={(e) => setNewKeyword(e.target.value)}
								onKeyDown={handleKeyPress}
								placeholder={t('words.keywords_placeholder')}
								className="h-11 py-2 text-sm rounded-xl focus:ring-2 focus:ring-primary/30 bg-background text-primary shadow-inner placeholder:text-primary/60"
								aria-label={t('words.keywords_placeholder')}
							/>
						</div>
						<Button
							onClick={handleAddKeyword}
							disabled={!newKeyword.trim()}
							size="sm"
							className="h-9 rounded-xl px-4 font-semibold bg-primary text-background shadow-lg hover:bg-primary/90 transition-all duration-200 flex gap-2 items-center text-sm"
						>
							<Plus className="h-4 w-4" />
							<Translate text="ui.add" />
						</Button>
					</div>

					<div className="space-y-3">
						<div className="min-h-[36px]">
							{!isFetchingKeywords && filteredKeywords.length > 0 ? (
								<div className="flex flex-wrap gap-3">
									{filteredKeywords.map((keyword) => {
										const isSelected = selectedKeywords.includes(keyword.id);
										return (
											<div key={keyword.id} className="relative">
												<span
													className="inline-flex items-center px-4 py-1.5 rounded-xl font-medium text-sm select-none transition-all duration-200 bg-primary text-background shadow-lg cursor-pointer"
													onClick={() => {
														markUserInteraction();
														if (isSelected) {
															handleRemoveKeyword(keyword.id);
														} else {
															setSelectedKeywords([
																...selectedKeywords,
																keyword.id,
															]);
														}
													}}
												>
													{keyword.content}
													<button
														onClick={(e) => {
															e.stopPropagation();
															handleDeleteKeyword(keyword.id);
														}}
														className="ml-2 hover:bg-background/20 rounded-full p-0.5 transition-colors"
														aria-label={`Delete ${keyword.content}`}
													>
														<X className="h-3 w-3" />
													</button>
												</span>
											</div>
										);
									})}
								</div>
							) : !isFetchingKeywords && filteredKeywords.length === 0 ? (
								<p className="text-muted-foreground text-sm italic">
									<Translate text="words.no_keywords_found" />
								</p>
							) : null}
						</div>
					</div>
				</div>
			</Card>
		</div>
	);
}
